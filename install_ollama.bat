@echo off
chcp 65001 >nul
title تثبيت Ollama - الذكاء الاصطناعي المجاني

echo.
echo ========================================
echo   تثبيت Ollama - الذكاء الاصطناعي المجاني
echo ========================================
echo.
echo 🤖 Ollama هو منصة مجانية لتشغيل نماذج AI محلياً
echo 💻 يعمل على جهازك بدون إنترنت
echo 🚀 يدعم نماذج متقدمة مثل Llama و Mistral
echo.

echo 📥 جاري تحميل Ollama...
echo.

REM تحميل Ollama
curl -L https://ollama.com/download/windows -o ollama-windows.exe

if exist ollama-windows.exe (
    echo ✅ تم تحميل Ollama بنجاح
    echo.
    echo 🔧 جاري تثبيت Ollama...
    start /wait ollama-windows.exe
    
    echo.
    echo ⏳ انتظار تشغيل Ollama...
    timeout /t 10 /nobreak >nul
    
    echo.
    echo 📦 جاري تحميل نموذج Llama2...
    ollama pull llama2
    
    echo.
    echo 📦 جاري تحميل نموذج Mistral...
    ollama pull mistral
    
    echo.
    echo ✅ تم تثبيت Ollama بنجاح!
    echo.
    echo 🎯 الآن يمكنك استخدام الذكاء الاصطناعي المحلي
    echo 🌐 شغل التطبيق واختر "ذكاء محلي"
    
) else (
    echo ❌ فشل في تحميل Ollama
    echo.
    echo 💡 يمكنك تحميله يدوياً من:
    echo https://ollama.com/download/windows
)

echo.
echo اضغط أي مفتاح للمتابعة...
pause >nul
