#!/usr/bin/env python3
"""
مولد العروض التقديمية بالذكاء الاصطناعي
تطبيق Flask لتحويل أي موضوع إلى عرض تقديمي احترافي
"""

import os
import sys
from app import app
from config import config
from dotenv import load_dotenv

# تحميل متغيرات البيئة من ملف .env
load_dotenv()

def main():
    """تشغيل التطبيق الرئيسي"""
    
    # تحديد بيئة التشغيل
    env = os.environ.get('FLASK_ENV', 'development')
    app.config.from_object(config.get(env, config['default']))
    
    # التحقق من وجود مفتاح OpenAI
    if not app.config.get('OPENAI_API_KEY') or app.config['OPENAI_API_KEY'] == 'YOUR_OPENAI_API_KEY':
        print("⚠️  تحذير: لم يتم تعيين مفتاح OpenAI API")
        print("📝 يرجى تعيين متغير البيئة OPENAI_API_KEY أو تعديل الكود")
        print("🔗 احصل على مفتاحك من: https://platform.openai.com/")
        print()
        
        # السماح بالمتابعة في بيئة التطوير
        if env == 'development':
            response = input("هل تريد المتابعة بدون مفتاح API؟ (y/N): ")
            if response.lower() != 'y':
                sys.exit(1)
        else:
            sys.exit(1)
    
    # إنشاء مجلد الرفع إذا لم يكن موجوداً
    upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
    
    # طباعة معلومات التشغيل
    print("🚀 بدء تشغيل مولد العروض التقديمية")
    print(f"🌍 البيئة: {env}")
    print(f"🔧 وضع التطوير: {app.config['DEBUG']}")
    print(f"🤖 نموذج OpenAI: {app.config.get('OPENAI_MODEL', 'غير محدد')}")
    print("=" * 50)
    
    # تشغيل التطبيق
    try:
        host = os.environ.get('HOST', '0.0.0.0')
        port = int(os.environ.get('PORT', 5000))
        
        print(f"🌐 التطبيق يعمل على: http://localhost:{port}")
        print("📱 يمكن الوصول إليه من أي جهاز على الشبكة المحلية")
        print("⏹️  اضغط Ctrl+C للإيقاف")
        print("=" * 50)
        
        app.run(
            host=host,
            port=port,
            debug=app.config['DEBUG'],
            threaded=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف التطبيق بنجاح")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
