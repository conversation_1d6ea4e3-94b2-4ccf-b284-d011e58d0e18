#!/usr/bin/env python3
"""
نسخة مبسطة من مولد العروض التقديمية
تستخدم GPT-3.5-turbo فقط بدون DALL-E لتوفير التكلفة
"""

from flask import Flask, request, send_file, render_template, jsonify
from pptx import Presentation
from pptx.util import Pt
from pptx.dml.color import RGBColor
import openai
import io
import os
import time
import uuid
from datetime import datetime
import json
import logging

# إعداد التطبيق
app = Flask(__name__)
app.secret_key = 'ai-presentation-generator-simple'

# إعداد OpenAI
openai.api_key = '********************************************************************************************************************************************************************'

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimplePresentationGenerator:
    def __init__(self):
        self.model = 'gpt-3.5-turbo'
    
    def detect_language(self, text):
        """كشف لغة النص"""
        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "Detect the language of the following text and respond with just the language code (ar, en, fr, es, etc.)"},
                    {"role": "user", "content": text}
                ],
                max_tokens=10
            )
            return response.choices[0].message.content.strip().lower()
        except:
            return 'en'
    
    def translate_to_english(self, text, source_lang):
        """ترجمة النص إلى الإنجليزية"""
        if source_lang == 'en':
            return text
        
        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": f"Translate the following text from {source_lang} to English. Provide only the translation."},
                    {"role": "user", "content": text}
                ],
                max_tokens=200
            )
            return response.choices[0].message.content.strip()
        except:
            return text
    
    def generate_presentation_content(self, topic):
        """توليد محتوى العرض التقديمي"""
        try:
            prompt = f"""
            Create a PowerPoint presentation about: {topic}
            
            Generate exactly 5 slides with the following structure:
            1. Introduction slide
            2-5. Content slides
            
            For each slide, provide:
            - Slide title (clear and engaging)
            - 3-4 bullet points with detailed content
            
            Format your response as JSON:
            {{
                "presentation_title": "{topic}",
                "slides": [
                    {{
                        "title": "slide title",
                        "content": ["point 1", "point 2", "point 3"]
                    }}
                ]
            }}
            
            Make it professional and informative.
            """
            
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a professional presentation designer. Create engaging and informative slide content."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1000,
                temperature=0.7
            )
            
            content = response.choices[0].message.content.strip()
            logger.info(f"Generated content for topic: {topic}")
            
            # استخراج JSON من الرد
            start = content.find('{')
            end = content.rfind('}') + 1
            if start == -1 or end == 0:
                raise ValueError("No valid JSON found in response")
            
            json_content = content[start:end]
            return json.loads(json_content)
            
        except Exception as e:
            logger.error(f"Error generating content: {e}")
            return self._get_fallback_content(topic)
    
    def _get_fallback_content(self, topic):
        """محتوى احتياطي في حالة فشل API"""
        return {
            "presentation_title": topic,
            "slides": [
                {
                    "title": f"مقدمة حول {topic}",
                    "content": [
                        "نظرة عامة على الموضوع",
                        "الأهداف الرئيسية", 
                        "الأهمية والصلة",
                        "ما سنتعلمه اليوم"
                    ]
                },
                {
                    "title": "النقاط الأساسية",
                    "content": [
                        "النقطة الأولى المهمة",
                        "النقطة الثانية الأساسية",
                        "النقطة الثالثة الرئيسية",
                        "النقطة الرابعة المكملة"
                    ]
                },
                {
                    "title": "التفاصيل والشرح",
                    "content": [
                        "شرح مفصل للموضوع",
                        "أمثلة عملية وتطبيقية",
                        "الفوائد والمزايا",
                        "التحديات والحلول"
                    ]
                },
                {
                    "title": "التطبيق العملي",
                    "content": [
                        "كيفية التطبيق في الواقع",
                        "الخطوات العملية",
                        "النصائح والإرشادات",
                        "أفضل الممارسات"
                    ]
                },
                {
                    "title": "الخلاصة والنتائج",
                    "content": [
                        "ملخص النقاط الرئيسية",
                        "النتائج المتوقعة",
                        "التوصيات المقترحة",
                        "الخطوات التالية"
                    ]
                }
            ]
        }
    
    def create_presentation(self, topic, content_data):
        """إنشاء العرض التقديمي"""
        prs = Presentation()
        
        # شريحة العنوان
        title_slide_layout = prs.slide_layouts[0]
        title_slide = prs.slides.add_slide(title_slide_layout)
        
        # تعيين عنوان العرض
        presentation_title = content_data.get('presentation_title', topic)
        title_slide.shapes.title.text = presentation_title
        
        # تنسيق عنوان الشريحة
        title_shape = title_slide.shapes.title
        title_shape.text_frame.paragraphs[0].font.size = Pt(32)
        title_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(44, 62, 80)
        
        # إضافة العنوان الفرعي
        if len(title_slide.placeholders) > 1:
            subtitle_text = f"تم إنشاؤه بالذكاء الاصطناعي • {datetime.now().strftime('%Y-%m-%d')}"
            title_slide.placeholders[1].text = subtitle_text
            subtitle_shape = title_slide.placeholders[1]
            subtitle_shape.text_frame.paragraphs[0].font.size = Pt(16)
            subtitle_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(127, 140, 141)
        
        # إضافة شرائح المحتوى
        for i, slide_data in enumerate(content_data['slides']):
            slide_layout = prs.slide_layouts[1]  # Title and Content layout
            slide = prs.slides.add_slide(slide_layout)
            
            # تعيين العنوان
            slide.shapes.title.text = slide_data['title']
            title_shape = slide.shapes.title
            title_shape.text_frame.paragraphs[0].font.size = Pt(28)
            title_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(44, 62, 80)
            
            # إضافة المحتوى
            if len(slide.placeholders) > 1:
                content_placeholder = slide.placeholders[1]
                text_frame = content_placeholder.text_frame
                text_frame.clear()
                
                for point in slide_data['content']:
                    p = text_frame.add_paragraph()
                    p.text = f"• {point}"
                    p.font.size = Pt(18)
                    p.font.color.rgb = RGBColor(52, 73, 94)
                    p.space_after = Pt(6)
            
            logger.info(f"Created slide {i+1}: {slide_data['title']}")
        
        logger.info(f"Presentation created successfully with {len(content_data['slides'])} slides")
        return prs

# إنشاء مولد العروض التقديمية
generator = SimplePresentationGenerator()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/generate', methods=['POST'])
def generate_presentation():
    try:
        data = request.get_json()
        topic = data.get('topic', '').strip()
        
        # التحقق من صحة البيانات
        if not topic:
            return jsonify({'error': 'الرجاء إدخال موضوع العرض'}), 400
        
        if len(topic) < 3:
            return jsonify({'error': 'موضوع العرض قصير جداً'}), 400
        
        if len(topic) > 500:
            return jsonify({'error': 'موضوع العرض طويل جداً'}), 400
        
        logger.info(f"Starting presentation generation for topic: {topic}")
        
        # كشف اللغة
        detected_lang = generator.detect_language(topic)
        logger.info(f"Detected language: {detected_lang}")
        
        # ترجمة إلى الإنجليزية إذا لزم الأمر
        topic_en = generator.translate_to_english(topic, detected_lang)
        if topic_en != topic:
            logger.info(f"Translated topic: {topic_en}")
        
        # توليد محتوى العرض
        content_data = generator.generate_presentation_content(topic_en)
        
        # إنشاء العرض التقديمي
        prs = generator.create_presentation(topic, content_data)
        
        # حفظ الملف
        file_stream = io.BytesIO()
        prs.save(file_stream)
        file_stream.seek(0)
        
        # إنشاء اسم ملف فريد
        safe_topic = "".join(c for c in topic[:20] if c.isalnum() or c in (' ', '-', '_')).strip()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{safe_topic}_{timestamp}.pptx"
        
        logger.info(f"Presentation generated successfully: {filename}")
        
        return send_file(
            file_stream,
            as_attachment=True,
            download_name=filename,
            mimetype="application/vnd.openxmlformats-officedocument.presentationml.presentation"
        )
        
    except Exception as e:
        logger.error(f"Error generating presentation: {e}")
        error_message = 'حدث خطأ في إنشاء العرض التقديمي'
        
        # رسائل خطأ محددة
        if 'api key' in str(e).lower():
            error_message = 'خطأ في مفتاح API - تأكد من صحة مفتاح OpenAI'
        elif 'timeout' in str(e).lower():
            error_message = 'انتهت مهلة الاتصال - حاول مرة أخرى'
        elif 'quota' in str(e).lower():
            error_message = 'تم تجاوز حد الاستخدام - حاول لاحقاً'
        
        return jsonify({'error': error_message}), 500

@app.route('/health')
def health_check():
    """فحص صحة التطبيق"""
    return jsonify({
        'status': 'healthy',
        'mode': 'simple',
        'timestamp': datetime.now().isoformat(),
        'model': 'gpt-3.5-turbo',
        'features': {
            'ai_content': True,
            'ai_images': False,
            'multi_language': True
        }
    })

if __name__ == '__main__':
    print("🚀 بدء تشغيل النسخة المبسطة من مولد العروض التقديمية")
    print("🌐 التطبيق يعمل على: http://localhost:5002")
    print("🤖 يستخدم: GPT-3.5-turbo (بدون صور)")
    print("💰 توفير في التكلفة: بدون DALL-E")
    print("=" * 60)
    app.run(debug=True, host='0.0.0.0', port=5002)
