import os
from datetime import timedelta

class Config:
    """إعدادات التطبيق الأساسية"""
    
    # إعدادات Flask
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-in-production'
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    # إعدادات OpenAI
    OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
    OPENAI_MODEL = os.environ.get('OPENAI_MODEL', 'gpt-3.5-turbo')
    OPENAI_MAX_TOKENS = int(os.environ.get('OPENAI_MAX_TOKENS', '1000'))
    OPENAI_TEMPERATURE = float(os.environ.get('OPENAI_TEMPERATURE', '0.7'))
    
    # إعدادات DALL-E
    DALLE_IMAGE_SIZE = os.environ.get('DALLE_IMAGE_SIZE', '1024x1024')
    DALLE_IMAGE_QUALITY = os.environ.get('DALLE_IMAGE_QUALITY', 'standard')
    
    # إعدادات العرض التقديمي
    DEFAULT_SLIDES_COUNT = int(os.environ.get('DEFAULT_SLIDES_COUNT', '6'))
    MAX_SLIDES_COUNT = int(os.environ.get('MAX_SLIDES_COUNT', '10'))
    MIN_SLIDES_COUNT = int(os.environ.get('MIN_SLIDES_COUNT', '3'))
    
    # إعدادات الملفات
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'uploads')
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None
    
    # إعدادات اللغات المدعومة
    SUPPORTED_LANGUAGES = {
        'ar': 'العربية',
        'en': 'English',
        'fr': 'Français',
        'es': 'Español',
        'de': 'Deutsch',
        'it': 'Italiano',
        'pt': 'Português',
        'ru': 'Русский',
        'ja': '日本語',
        'ko': '한국어',
        'zh': '中文',
        'hi': 'हिन्दी',
        'tr': 'Türkçe',
        'nl': 'Nederlands',
        'sv': 'Svenska',
        'da': 'Dansk',
        'no': 'Norsk',
        'fi': 'Suomi',
        'pl': 'Polski',
        'cs': 'Čeština',
        'hu': 'Magyar',
        'ro': 'Română',
        'bg': 'Български',
        'hr': 'Hrvatski',
        'sk': 'Slovenčina',
        'sl': 'Slovenščina',
        'et': 'Eesti',
        'lv': 'Latviešu',
        'lt': 'Lietuvių',
        'mt': 'Malti',
        'ga': 'Gaeilge',
        'cy': 'Cymraeg',
        'eu': 'Euskera',
        'ca': 'Català',
        'gl': 'Galego',
        'is': 'Íslenska',
        'fo': 'Føroyskt',
        'mk': 'Македонски',
        'sq': 'Shqip',
        'sr': 'Српски',
        'bs': 'Bosanski',
        'me': 'Crnogorski',
        'uk': 'Українська',
        'be': 'Беларуская',
        'kk': 'Қазақша',
        'ky': 'Кыргызча',
        'uz': 'O\'zbekcha',
        'tg': 'Тоҷикӣ',
        'mn': 'Монгол',
        'ka': 'ქართული',
        'hy': 'Հայերեն',
        'az': 'Azərbaycan',
        'he': 'עברית',
        'fa': 'فارسی',
        'ur': 'اردو',
        'ps': 'پښتو',
        'sd': 'سنڌي',
        'ks': 'कॉशुर',
        'ne': 'नेपाली',
        'si': 'සිංහල',
        'my': 'မြန်မာ',
        'km': 'ខ្មែរ',
        'lo': 'ລາວ',
        'th': 'ไทย',
        'vi': 'Tiếng Việt',
        'id': 'Bahasa Indonesia',
        'ms': 'Bahasa Melayu',
        'tl': 'Filipino',
        'haw': 'ʻŌlelo Hawaiʻi',
        'mi': 'Te Reo Māori',
        'sm': 'Gagana Samoa',
        'to': 'Lea Fakatonga',
        'fj': 'Na Vosa Vakaviti',
        'sw': 'Kiswahili',
        'zu': 'isiZulu',
        'xh': 'isiXhosa',
        'af': 'Afrikaans',
        'st': 'Sesotho',
        'tn': 'Setswana',
        'ss': 'siSwati',
        've': 'Tshivenḓa',
        'ts': 'Xitsonga',
        'nr': 'isiNdebele',
        'nso': 'Sepedi',
        'am': 'አማርኛ',
        'ti': 'ትግርኛ',
        'om': 'Afaan Oromoo',
        'so': 'Soomaali',
        'ha': 'Hausa',
        'yo': 'Yorùbá',
        'ig': 'Igbo',
        'ff': 'Fulfulde',
        'wo': 'Wolof',
        'bm': 'Bamanankan',
        'rn': 'Kirundi',
        'rw': 'Kinyarwanda',
        'lg': 'Luganda',
        'ak': 'Akan',
        'tw': 'Twi',
        'ee': 'Eʋegbe',
        'gaa': 'Ga',
        'dag': 'Dagbani',
        'kri': 'Krio'
    }
    
    # قوالب العروض التقديمية
    PRESENTATION_TEMPLATES = {
        'business': {
            'name': 'Business Professional',
            'description': 'قالب احترافي للأعمال',
            'colors': ['#2C3E50', '#3498DB', '#E74C3C', '#F39C12'],
            'font_size': {'title': 32, 'content': 18}
        },
        'education': {
            'name': 'Educational',
            'description': 'قالب تعليمي',
            'colors': ['#27AE60', '#8E44AD', '#E67E22', '#34495E'],
            'font_size': {'title': 30, 'content': 16}
        },
        'creative': {
            'name': 'Creative Design',
            'description': 'قالب إبداعي',
            'colors': ['#E91E63', '#9C27B0', '#673AB7', '#3F51B5'],
            'font_size': {'title': 28, 'content': 17}
        },
        'minimal': {
            'name': 'Minimal Clean',
            'description': 'قالب بسيط ونظيف',
            'colors': ['#607D8B', '#455A64', '#37474F', '#263238'],
            'font_size': {'title': 26, 'content': 15}
        }
    }

class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    DEBUG = False
    TESTING = False
    
    # إعدادات أمان إضافية للإنتاج
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    TESTING = True
    DEBUG = True
    WTF_CSRF_ENABLED = False

# اختيار الإعدادات حسب البيئة
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
