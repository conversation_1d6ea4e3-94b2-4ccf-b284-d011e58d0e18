from flask import Flask, request, send_file, render_template_string
from pptx import Presentation
from pptx.util import Pt
from pptx.dml.color import RGBColor
import io
from datetime import datetime

app = Flask(__name__)

HTML = '''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>مولد العروض التقديمية</title>
    <style>
        body { 
            font-family: Arial; 
            background: #2c3e50; 
            padding: 20px; 
            color: white; 
        }
        .container { 
            background: white; 
            color: black; 
            padding: 30px; 
            border-radius: 10px; 
            max-width: 500px; 
            margin: 0 auto; 
        }
        h1 { 
            color: #e74c3c; 
            text-align: center; 
            font-size: 2em;
        }
        .success { 
            background: #27ae60; 
            color: white; 
            padding: 10px; 
            border-radius: 5px; 
            margin: 10px 0; 
            text-align: center;
        }
        textarea { 
            width: 100%; 
            padding: 10px; 
            height: 80px; 
            border: 2px solid #3498db; 
            border-radius: 5px; 
            font-size: 14px;
            box-sizing: border-box;
        }
        button { 
            width: 100%; 
            padding: 15px; 
            background: #e74c3c; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            font-size: 16px; 
            margin-top: 10px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>مولد العروض التقديمية</h1>
        <div class="success">نسخة بسيطة - تعمل مضمون</div>
        
        <form method="post">
            <label>اكتب الموضوع:</label><br><br>
            <textarea name="topic" placeholder="مثال: التعامل مع الاطفال المشاغبين" required></textarea><br>
            <button type="submit">انشاء العرض</button>
        </form>
    </div>
</body>
</html>'''

def create_simple_ppt(topic):
    prs = Presentation()
    
    # الوان
    blue = RGBColor(52, 152, 219)
    dark = RGBColor(44, 62, 80)
    
    # شريحة العنوان
    title_slide = prs.slides.add_slide(prs.slide_layouts[0])
    title_slide.shapes.title.text = topic
    title_slide.shapes.title.text_frame.paragraphs[0].font.size = Pt(32)
    title_slide.shapes.title.text_frame.paragraphs[0].font.color.rgb = blue
    
    if len(title_slide.placeholders) > 1:
        title_slide.placeholders[1].text = f"عرض تقديمي - {datetime.now().strftime('%Y-%m-%d')}"
    
    # شرائح المحتوى
    content_slides = [
        {
            'title': 'المقدمة',
            'points': [
                'تعريف الموضوع واهميته في الوقت الحالي',
                'الاهداف المرجوة من هذا العرض التقديمي',
                'نظرة عامة على النقاط التي سيتم تناولها',
                'اهمية تطبيق هذه المعرفة في الحياة العملية'
            ]
        },
        {
            'title': 'التحليل',
            'points': [
                'تحليل العوامل والاسباب المؤثرة على الموضوع',
                'دراسة الجوانب المختلفة والابعاد المتعددة',
                'فحص الحالات والتجارب السابقة والدروس المستفادة',
                'تحديد التحديات والصعوبات المحتملة'
            ]
        },
        {
            'title': 'الحلول',
            'points': [
                'وضع استراتيجيات عملية وقابلة للتطبيق',
                'تطوير خطط عمل واضحة ومحددة',
                'استخدام افضل الممارسات والتجارب الناجحة',
                'تحديد الادوات والموارد المطلوبة'
            ]
        },
        {
            'title': 'التطبيق',
            'points': [
                'خطوات التطبيق العملي في البيئة الحقيقية',
                'كيفية التنفيذ الفعال مع مراعاة الظروف',
                'طرق قياس النتائج والتقدم بشكل دوري',
                'التعديل والتحسين المستمر'
            ]
        },
        {
            'title': 'الخلاصة',
            'points': [
                'ملخص النقاط الرئيسية والاستنتاجات',
                'التوصيات العملية للخطوات التالية',
                'نصائح مهمة للنجاح في التطبيق',
                'المراجع والمصادر للاستزادة'
            ]
        }
    ]
    
    # انشاء الشرائح
    for slide_data in content_slides:
        slide = prs.slides.add_slide(prs.slide_layouts[1])
        
        # العنوان
        slide.shapes.title.text = slide_data['title']
        slide.shapes.title.text_frame.paragraphs[0].font.size = Pt(28)
        slide.shapes.title.text_frame.paragraphs[0].font.color.rgb = blue
        slide.shapes.title.text_frame.paragraphs[0].font.bold = True
        
        # النقاط
        if len(slide.placeholders) > 1:
            content_placeholder = slide.placeholders[1]
            text_frame = content_placeholder.text_frame
            text_frame.clear()
            
            for i, point in enumerate(slide_data['points']):
                if i == 0:
                    p = text_frame.paragraphs[0]
                else:
                    p = text_frame.add_paragraph()
                
                p.text = f"• {point}"
                p.font.size = Pt(16)
                p.font.color.rgb = dark
                p.space_after = Pt(8)
    
    return prs

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        topic = request.form.get('topic', '').strip()
        
        if not topic:
            return "ادخل موضوع", 400
        
        try:
            prs = create_simple_ppt(topic)
            
            file_stream = io.BytesIO()
            prs.save(file_stream)
            file_stream.seek(0)
            
            filename = f"presentation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pptx"
            
            return send_file(
                file_stream,
                as_attachment=True,
                download_name=filename,
                mimetype="application/vnd.openxmlformats-officedocument.presentationml.presentation"
            )
            
        except Exception as e:
            return f"خطأ: {str(e)}", 500
    
    return render_template_string(HTML)

if __name__ == '__main__':
    print("تشغيل النسخة البسيطة")
    print("http://localhost:7000")
    print("نسخة مبسطة تعمل مضمون")
    
    app.run(debug=True, host='0.0.0.0', port=7000)
