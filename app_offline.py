#!/usr/bin/env python3
"""
نسخة تعمل بدون اتصال من مولد العروض التقديمية
تعمل بدون OpenAI API - محتوى محدد مسبقاً
"""

from flask import Flask, request, send_file, render_template_string
from pptx import Presentation
from pptx.util import Pt
from pptx.dml.color import RGBColor
import io
import uuid
from datetime import datetime
import re

app = Flask(__name__)

# HTML للواجهة
OFFLINE_HTML = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد العروض التقديمية - نسخة بدون اتصال</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .offline-badge {
            background: #27ae60;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            display: inline-block;
            margin-bottom: 15px;
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
        }
        .form-group textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e6ed;
            border-radius: 10px;
            font-size: 16px;
            resize: vertical;
            min-height: 100px;
        }
        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
        }
        .note {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #27ae60;
        }
        .examples {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }
        .examples h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .examples ul {
            list-style-type: none;
            padding: 0;
        }
        .examples li {
            padding: 5px 0;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="offline-badge">يعمل بدون اتصال</div>
            <h1>🎯 مولد العروض التقديمية</h1>
            <p>نسخة تعمل بدون الحاجة لإنترنت أو API</p>
        </div>

        <form method="post">
            <div class="form-group">
                <label for="topic">موضوع العرض التقديمي:</label>
                <textarea id="topic" name="topic" placeholder="مثال: الذكاء الاصطناعي في التعليم" required></textarea>
            </div>
            <button type="submit" class="generate-btn">إنشاء العرض التقديمي</button>
        </form>

        <div class="note">
            <h3>✅ مميزات هذه النسخة:</h3>
            <p>• تعمل بدون اتصال بالإنترنت<br>
            • لا تحتاج مفتاح API<br>
            • سريعة ومجانية تماماً<br>
            • تنشئ عروض تقديمية احترافية</p>
        </div>

        <div class="examples">
            <h4>💡 أمثلة للمواضيع:</h4>
            <ul>
                <li>• الذكاء الاصطناعي في التعليم</li>
                <li>• إدارة الوقت بفعالية</li>
                <li>• التسويق الرقمي</li>
                <li>• البرمجة للمبتدئين</li>
                <li>• الصحة النفسية</li>
            </ul>
        </div>
    </div>
</body>
</html>
'''

class OfflinePresentationGenerator:
    def __init__(self):
        # قوالب محتوى محددة مسبقاً
        self.content_templates = {
            'تعليم': {
                'keywords': ['تعليم', 'تعلم', 'دراسة', 'مدرسة', 'جامعة', 'طلاب', 'education', 'learning', 'school'],
                'slides': [
                    {
                        'title': 'مقدمة في التعليم',
                        'content': [
                            'أهمية التعليم في المجتمع',
                            'أهداف العملية التعليمية',
                            'التحديات المعاصرة في التعليم',
                            'الفرص المتاحة للتطوير'
                        ]
                    },
                    {
                        'title': 'طرق التعليم الحديثة',
                        'content': [
                            'التعلم التفاعلي والمشاركة',
                            'استخدام التكنولوجيا في التعليم',
                            'التعلم القائم على المشاريع',
                            'التقييم المستمر والتغذية الراجعة'
                        ]
                    },
                    {
                        'title': 'التحديات والحلول',
                        'content': [
                            'تحديات الموارد والإمكانيات',
                            'الفروق الفردية بين الطلاب',
                            'مواكبة التطورات التقنية',
                            'تطوير مهارات المعلمين'
                        ]
                    },
                    {
                        'title': 'مستقبل التعليم',
                        'content': [
                            'التعليم الرقمي والتعلم عن بُعد',
                            'الذكاء الاصطناعي في التعليم',
                            'التعلم المدى الحياة',
                            'المهارات المطلوبة للمستقبل'
                        ]
                    }
                ]
            },
            'تقنية': {
                'keywords': ['تقنية', 'تكنولوجيا', 'برمجة', 'حاسوب', 'ذكي', 'technology', 'programming', 'ai', 'computer'],
                'slides': [
                    {
                        'title': 'مقدمة في التقنية',
                        'content': [
                            'تعريف التقنية وأهميتها',
                            'تطور التقنية عبر التاريخ',
                            'تأثير التقنية على حياتنا',
                            'التقنيات الناشئة والمستقبلية'
                        ]
                    },
                    {
                        'title': 'التطبيقات العملية',
                        'content': [
                            'التقنية في الطب والصحة',
                            'التقنية في التعليم والتدريب',
                            'التقنية في الأعمال والتجارة',
                            'التقنية في الترفيه والإعلام'
                        ]
                    },
                    {
                        'title': 'التحديات والفرص',
                        'content': [
                            'الأمن السيبراني والخصوصية',
                            'الفجوة الرقمية والمساواة',
                            'التأثير على سوق العمل',
                            'الاستدامة والبيئة'
                        ]
                    },
                    {
                        'title': 'المستقبل التقني',
                        'content': [
                            'الذكاء الاصطناعي والتعلم الآلي',
                            'إنترنت الأشياء والمدن الذكية',
                            'الواقع المعزز والافتراضي',
                            'الحوسبة الكمية والبلوك تشين'
                        ]
                    }
                ]
            },
            'عام': {
                'keywords': [],  # الافتراضي
                'slides': [
                    {
                        'title': 'مقدمة عن الموضوع',
                        'content': [
                            'نظرة عامة وتعريف أساسي',
                            'الأهمية والصلة بالواقع',
                            'الأهداف المرجوة من الدراسة',
                            'الخطة والمنهجية المتبعة'
                        ]
                    },
                    {
                        'title': 'النقاط الرئيسية',
                        'content': [
                            'النقطة الأولى والأساسية',
                            'النقطة الثانية والمكملة',
                            'النقطة الثالثة والداعمة',
                            'النقطة الرابعة والختامية'
                        ]
                    },
                    {
                        'title': 'التحليل والمناقشة',
                        'content': [
                            'تحليل البيانات والمعلومات',
                            'مناقشة النتائج والاستنتاجات',
                            'المقارنة مع الدراسات السابقة',
                            'التفسير والتبرير المنطقي'
                        ]
                    },
                    {
                        'title': 'الخلاصة والتوصيات',
                        'content': [
                            'ملخص النقاط الرئيسية',
                            'النتائج والاستنتاجات النهائية',
                            'التوصيات والاقتراحات',
                            'الخطوات التالية والمتابعة'
                        ]
                    }
                ]
            }
        }
    
    def detect_topic_category(self, topic):
        """كشف فئة الموضوع"""
        topic_lower = topic.lower()
        
        for category, template in self.content_templates.items():
            if category == 'عام':
                continue
            for keyword in template['keywords']:
                if keyword in topic_lower:
                    return category
        
        return 'عام'
    
    def generate_content(self, topic):
        """توليد محتوى العرض"""
        category = self.detect_topic_category(topic)
        template = self.content_templates[category]
        
        # تخصيص المحتوى حسب الموضوع
        slides = []
        for slide_template in template['slides']:
            slide = {
                'title': slide_template['title'].replace('الموضوع', topic),
                'content': [point.replace('الموضوع', topic) for point in slide_template['content']]
            }
            slides.append(slide)
        
        return {
            'presentation_title': topic,
            'category': category,
            'slides': slides
        }
    
    def create_presentation(self, topic, content_data):
        """إنشاء العرض التقديمي"""
        prs = Presentation()
        
        # شريحة العنوان
        title_slide_layout = prs.slide_layouts[0]
        title_slide = prs.slides.add_slide(title_slide_layout)
        
        title_slide.shapes.title.text = content_data['presentation_title']
        
        # تنسيق العنوان
        title_shape = title_slide.shapes.title
        title_shape.text_frame.paragraphs[0].font.size = Pt(32)
        title_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(44, 62, 80)
        
        # العنوان الفرعي
        if len(title_slide.placeholders) > 1:
            subtitle_text = f"عرض تقديمي • {datetime.now().strftime('%Y-%m-%d')}"
            title_slide.placeholders[1].text = subtitle_text
            subtitle_shape = title_slide.placeholders[1]
            subtitle_shape.text_frame.paragraphs[0].font.size = Pt(16)
            subtitle_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(127, 140, 141)
        
        # شرائح المحتوى
        for i, slide_data in enumerate(content_data['slides']):
            slide_layout = prs.slide_layouts[1]
            slide = prs.slides.add_slide(slide_layout)
            
            # العنوان
            slide.shapes.title.text = slide_data['title']
            title_shape = slide.shapes.title
            title_shape.text_frame.paragraphs[0].font.size = Pt(28)
            title_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(44, 62, 80)
            
            # المحتوى
            if len(slide.placeholders) > 1:
                content_placeholder = slide.placeholders[1]
                text_frame = content_placeholder.text_frame
                text_frame.clear()
                
                for point in slide_data['content']:
                    p = text_frame.add_paragraph()
                    p.text = f"• {point}"
                    p.font.size = Pt(18)
                    p.font.color.rgb = RGBColor(52, 73, 94)
                    p.space_after = Pt(6)
        
        return prs

# إنشاء مولد العروض
generator = OfflinePresentationGenerator()

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        topic = request.form.get('topic', '').strip()
        
        if not topic:
            return "الرجاء إدخال موضوع العرض", 400
        
        # توليد المحتوى
        content_data = generator.generate_content(topic)
        
        # إنشاء العرض
        prs = generator.create_presentation(topic, content_data)
        
        # حفظ الملف
        file_stream = io.BytesIO()
        prs.save(file_stream)
        file_stream.seek(0)
        
        # اسم الملف
        safe_topic = "".join(c for c in topic[:20] if c.isalnum() or c in (' ', '-', '_')).strip()
        filename = f"offline_{safe_topic}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pptx"
        
        return send_file(
            file_stream,
            as_attachment=True,
            download_name=filename,
            mimetype="application/vnd.openxmlformats-officedocument.presentationml.presentation"
        )
    
    return render_template_string(OFFLINE_HTML)

@app.route('/health')
def health():
    return {
        'status': 'healthy',
        'mode': 'offline',
        'timestamp': datetime.now().isoformat(),
        'note': 'Works without internet or API'
    }

if __name__ == '__main__':
    print("🚀 تشغيل النسخة التي تعمل بدون اتصال")
    print("🌐 التطبيق يعمل على: http://localhost:5003")
    print("✅ لا يحتاج إنترنت أو API")
    print("💡 محتوى ذكي محدد مسبقاً")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5003)
