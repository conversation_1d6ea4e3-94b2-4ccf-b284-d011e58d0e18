#!/usr/bin/env python3
"""
قائمة تشغيل شاملة لجميع إصدارات مولد العروض التقديمية
"""

import os
import sys
import subprocess
import time

def print_header():
    print("=" * 70)
    print("🎯 مولد العروض التقديمية بالذكاء الاصطناعي")
    print("=" * 70)
    print()

def print_options():
    print("📋 اختر الإصدار المناسب لك:")
    print()
    print("1️⃣  النسخة الكاملة (GPT-4 + DALL-E)")
    print("   • أفضل جودة وذكاء اصطناعي متقدم")
    print("   • يحتاج مفتاح OpenAI API ورصيد كافي")
    print("   • المنفذ: http://localhost:5000")
    print()
    
    print("2️⃣  النسخة المبسطة (GPT-3.5-turbo فقط)")
    print("   • ذكاء اصطناعي جيد بدون صور")
    print("   • أقل تكلفة من النسخة الكاملة")
    print("   • المنفذ: http://localhost:5002")
    print()
    
    print("3️⃣  النسخة بدون اتصال (محتوى محدد مسبقاً)")
    print("   • تعمل بدون إنترنت أو API")
    print("   • مجانية تماماً وسريعة")
    print("   • المنفذ: http://localhost:5003")
    print()
    
    print("4️⃣  النسخة التجريبية (أساسية)")
    print("   • للتجربة السريعة")
    print("   • بدون ذكاء اصطناعي")
    print("   • المنفذ: http://localhost:5001")
    print()
    
    print("0️⃣  خروج")
    print()

def check_requirements():
    """التحقق من المتطلبات الأساسية"""
    try:
        import flask
        import pptx
        print("✅ المكتبات الأساسية متوفرة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("💡 قم بتثبيت المتطلبات: pip install flask python-pptx")
        return False

def check_openai():
    """التحقق من توفر OpenAI"""
    try:
        import openai
        api_key = os.environ.get('OPENAI_API_KEY')
        if api_key and api_key != 'YOUR_OPENAI_API_KEY':
            print("✅ مفتاح OpenAI API متوفر")
            return True
        else:
            print("⚠️  مفتاح OpenAI API غير متوفر")
            return False
    except ImportError:
        print("⚠️  مكتبة OpenAI غير مثبتة")
        return False

def run_app(app_file, port, description):
    """تشغيل تطبيق محدد"""
    print(f"🚀 بدء تشغيل {description}")
    print(f"🌐 سيفتح على: http://localhost:{port}")
    print("⏹️  اضغط Ctrl+C للإيقاف")
    print("=" * 50)
    
    try:
        subprocess.run([sys.executable, app_file], cwd=os.getcwd())
    except KeyboardInterrupt:
        print(f"\n👋 تم إيقاف {description}")
    except Exception as e:
        print(f"❌ خطأ في تشغيل {description}: {e}")

def main():
    print_header()
    
    # التحقق من المتطلبات
    if not check_requirements():
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من OpenAI
    has_openai = check_openai()
    
    while True:
        print_options()
        
        try:
            choice = input("اختر رقم الإصدار (1-4) أو 0 للخروج: ").strip()
            
            if choice == '0':
                print("👋 شكراً لاستخدام مولد العروض التقديمية!")
                break
            
            elif choice == '1':
                if has_openai:
                    run_app('start_simple.py', 5000, 'النسخة الكاملة')
                else:
                    print("❌ النسخة الكاملة تحتاج مفتاح OpenAI API")
                    print("💡 استخدم النسخة بدون اتصال (خيار 3) بدلاً من ذلك")
            
            elif choice == '2':
                if has_openai:
                    run_app('app_simple.py', 5002, 'النسخة المبسطة')
                else:
                    print("❌ النسخة المبسطة تحتاج مفتاح OpenAI API")
                    print("💡 استخدم النسخة بدون اتصال (خيار 3) بدلاً من ذلك")
            
            elif choice == '3':
                run_app('app_offline.py', 5003, 'النسخة بدون اتصال')
            
            elif choice == '4':
                run_app('demo_app.py', 5001, 'النسخة التجريبية')
            
            else:
                print("❌ اختيار غير صحيح. الرجاء اختيار رقم من 0-4")
            
            print("\n" + "=" * 50)
            input("اضغط Enter للعودة للقائمة الرئيسية...")
            print("\n")
            
        except KeyboardInterrupt:
            print("\n👋 تم الخروج من البرنامج")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
