from flask import Flask, request, send_file, render_template, jsonify, session
from pptx import Presentation
from pptx.util import Pt, Inches
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from pptx.enum.shapes import MSO_SHAPE
import openai
import io
import os
import time
import requests
from io import BytesIO
import uuid
from datetime import datetime
import json
import logging
from config import config
from dotenv import load_dotenv

# تحميل متغيرات البيئة من ملف .env
load_dotenv()

# إعداد التطبيق
app = Flask(__name__)

# تحديد بيئة التشغيل وتطبيق الإعدادات
env = os.environ.get('FLASK_ENV', 'development')
app.config.from_object(config.get(env, config['default']))

# إعداد OpenAI
openai.api_key = app.config['OPENAI_API_KEY']

# إعداد نظام السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PresentationGenerator:
    def __init__(self):
        self.supported_languages = app.config['SUPPORTED_LANGUAGES']
        self.templates = app.config['PRESENTATION_TEMPLATES']
    
    def detect_language(self, text):
        """كشف لغة النص باستخدام OpenAI"""
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "Detect the language of the following text and respond with just the language code (ar, en, fr, es, etc.)"},
                    {"role": "user", "content": text}
                ],
                max_tokens=10
            )
            return response.choices[0].message.content.strip().lower()
        except:
            return 'en'  # افتراضي
    
    def translate_to_english(self, text, source_lang):
        """ترجمة النص إلى الإنجليزية"""
        if source_lang == 'en':
            return text
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": f"Translate the following text from {source_lang} to English. Provide only the translation."},
                    {"role": "user", "content": text}
                ],
                max_tokens=200
            )
            return response.choices[0].message.content.strip()
        except:
            return text
    
    def generate_presentation_content(self, topic, original_language, slides_count=None):
        """توليد محتوى العرض التقديمي"""
        if slides_count is None:
            slides_count = app.config['DEFAULT_SLIDES_COUNT']

        try:
            prompt = f"""
            Create a comprehensive PowerPoint presentation about: {topic}

            Generate exactly {slides_count} slides with the following structure:
            1. Title slide (if included in count)
            2-{slides_count}. Content slides

            For each slide, provide:
            - Slide title (clear and engaging)
            - 3-4 bullet points with detailed, informative content
            - A description for background image (professional, relevant to slide content)

            Format your response as JSON:
            {{
                "presentation_title": "{topic}",
                "slides": [
                    {{
                        "title": "slide title",
                        "content": ["point 1", "point 2", "point 3", "point 4"],
                        "image_prompt": "professional background image description"
                    }}
                ]
            }}

            Make it professional, informative, and engaging. Ensure content is well-structured and flows logically.
            """

            response = openai.ChatCompletion.create(
                model=app.config['OPENAI_MODEL'],
                messages=[
                    {"role": "system", "content": "You are a professional presentation designer with expertise in creating engaging, informative slide content. Focus on clarity, structure, and visual appeal."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=app.config['OPENAI_MAX_TOKENS'],
                temperature=app.config['OPENAI_TEMPERATURE']
            )

            content = response.choices[0].message.content.strip()
            logger.info(f"Generated content for topic: {topic}")

            # استخراج JSON من الرد
            start = content.find('{')
            end = content.rfind('}') + 1
            if start == -1 or end == 0:
                raise ValueError("No valid JSON found in response")

            json_content = content[start:end]
            return json.loads(json_content)

        except Exception as e:
            logger.error(f"Error generating content: {e}")
            return self._get_fallback_content(topic, slides_count)
    
    def _get_fallback_content(self, topic, slides_count=6):
        """محتوى احتياطي في حالة فشل API"""
        slides = []

        # شريحة المقدمة
        slides.append({
            "title": f"Introduction to {topic}",
            "content": [
                "Overview of the topic",
                "Key concepts and definitions",
                "Importance and relevance",
                "What we'll cover today"
            ],
            "image_prompt": f"Professional introduction background about {topic}"
        })

        # شرائح المحتوى
        for i in range(2, slides_count + 1):
            slides.append({
                "title": f"Key Point {i-1}",
                "content": [
                    f"Main concept {i-1}",
                    f"Supporting details",
                    f"Examples and applications",
                    f"Benefits and implications"
                ],
                "image_prompt": f"Professional background for {topic} concept {i-1}"
            })

        return {
            "presentation_title": topic,
            "slides": slides
        }
    
    def generate_background_image(self, prompt, slide_title=""):
        """توليد صورة خلفية باستخدام DALL-E"""
        try:
            # تحسين وصف الصورة
            enhanced_prompt = f"""
            Professional presentation background image for slide titled '{slide_title}'.
            {prompt}.
            Style: Clean, modern, corporate design with subtle gradients.
            Colors: Professional blue and white tones.
            Layout: Suitable for text overlay, not too busy.
            Quality: High resolution, 16:9 aspect ratio.
            Avoid: Text, logos, or distracting elements.
            """

            response = openai.Image.create(
                prompt=enhanced_prompt.strip(),
                n=1,
                size=app.config['DALLE_IMAGE_SIZE'],
                quality=app.config.get('DALLE_IMAGE_QUALITY', 'standard')
            )

            image_url = response['data'][0]['url']
            img_response = requests.get(image_url, timeout=30)

            if img_response.status_code == 200:
                logger.info(f"Generated background image for: {slide_title}")
                return img_response.content
            else:
                logger.warning(f"Failed to download image: {img_response.status_code}")
                return None

        except Exception as e:
            logger.error(f"Error generating image for '{slide_title}': {e}")
            return None
    
    def create_presentation(self, topic, content_data, original_language, template='business'):
        """إنشاء العرض التقديمي"""
        prs = Presentation()

        # الحصول على إعدادات القالب
        template_config = self.templates.get(template, self.templates['business'])
        colors = template_config['colors']
        font_sizes = template_config['font_size']

        # شريحة العنوان
        title_slide_layout = prs.slide_layouts[0]
        title_slide = prs.slides.add_slide(title_slide_layout)

        # تعيين عنوان العرض
        presentation_title = content_data.get('presentation_title', topic)
        title_slide.shapes.title.text = presentation_title

        # تنسيق عنوان الشريحة
        title_shape = title_slide.shapes.title
        title_shape.text_frame.paragraphs[0].font.size = Pt(font_sizes['title'])
        # إصلاح مشكلة الألوان
        try:
            if colors[0].startswith('#'):
                color_hex = colors[0][1:]  # إزالة #
                if len(color_hex) == 6:
                    r = int(color_hex[0:2], 16)
                    g = int(color_hex[2:4], 16)
                    b = int(color_hex[4:6], 16)
                    title_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(r, g, b)
        except:
            title_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(44, 62, 80)

        # إضافة العنوان الفرعي
        if len(title_slide.placeholders) > 1:
            subtitle_text = f"Generated by AI • {datetime.now().strftime('%B %d, %Y')}"
            if original_language == 'ar':
                subtitle_text = f"تم إنشاؤه بالذكاء الاصطناعي • {datetime.now().strftime('%Y-%m-%d')}"

            title_slide.placeholders[1].text = subtitle_text
            subtitle_shape = title_slide.placeholders[1]
            subtitle_shape.text_frame.paragraphs[0].font.size = Pt(16)
            # إصلاح مشكلة الألوان
            try:
                if colors[1].startswith('#'):
                    color_hex = colors[1][1:]
                    if len(color_hex) == 6:
                        r = int(color_hex[0:2], 16)
                        g = int(color_hex[2:4], 16)
                        b = int(color_hex[4:6], 16)
                        subtitle_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(r, g, b)
            except:
                subtitle_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(127, 140, 141)

        # إضافة خلفية لشريحة العنوان
        title_bg_prompt = f"Professional title slide background for presentation about {topic}"
        title_img_data = self.generate_background_image(title_bg_prompt, "Title Slide")
        if title_img_data:
            self._add_background_image(title_slide, title_img_data, prs)

        # إضافة شرائح المحتوى
        for i, slide_data in enumerate(content_data['slides']):
            slide_layout = prs.slide_layouts[1]  # Title and Content layout
            slide = prs.slides.add_slide(slide_layout)

            # تعيين العنوان
            slide.shapes.title.text = slide_data['title']
            title_shape = slide.shapes.title
            title_shape.text_frame.paragraphs[0].font.size = Pt(font_sizes['title'] - 4)
            # إصلاح مشكلة الألوان
            try:
                if colors[0].startswith('#'):
                    color_hex = colors[0][1:]
                    if len(color_hex) == 6:
                        r = int(color_hex[0:2], 16)
                        g = int(color_hex[2:4], 16)
                        b = int(color_hex[4:6], 16)
                        title_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(r, g, b)
            except:
                title_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(44, 62, 80)

            # إضافة المحتوى
            if len(slide.placeholders) > 1:
                content_placeholder = slide.placeholders[1]
                text_frame = content_placeholder.text_frame
                text_frame.clear()

                for point in slide_data['content']:
                    p = text_frame.add_paragraph()
                    p.text = f"• {point}"
                    p.font.size = Pt(font_sizes['content'])
                    # إصلاح مشكلة الألوان
                    try:
                        if len(colors) > 2 and colors[2].startswith('#'):
                            color_hex = colors[2][1:]
                            if len(color_hex) == 6:
                                r = int(color_hex[0:2], 16)
                                g = int(color_hex[2:4], 16)
                                b = int(color_hex[4:6], 16)
                                p.font.color.rgb = RGBColor(r, g, b)
                    except:
                        p.font.color.rgb = RGBColor(52, 73, 94)
                    p.space_after = Pt(6)  # مسافة بين النقاط

            # إضافة خلفية للشريحة
            if 'image_prompt' in slide_data:
                img_data = self.generate_background_image(
                    slide_data['image_prompt'],
                    slide_data['title']
                )
                if img_data:
                    self._add_background_image(slide, img_data, prs)

            # تأخير لتجنب تجاوز حدود API
            time.sleep(1)
            logger.info(f"Created slide {i+1}: {slide_data['title']}")

        logger.info(f"Presentation created successfully with {len(content_data['slides'])} slides")
        return prs
    
    def _add_background_image(self, slide, img_data, prs):
        """إضافة صورة خلفية للشريحة"""
        try:
            img_stream = BytesIO(img_data)

            # إضافة الصورة كخلفية
            picture = slide.shapes.add_picture(
                img_stream,
                0, 0,
                width=prs.slide_width,
                height=prs.slide_height
            )

            # نقل الصورة للخلف (خلف النصوص)
            slide.shapes._spTree.insert(2, slide.shapes._spTree.pop())

            # إضافة شفافية خفيفة للصورة لتحسين قراءة النص
            # هذا يتطلب تعديل XML مباشر
            try:
                pic_element = picture._element
                # إضافة تأثير شفافية
                pic_element.set('transparency', '20000')  # 20% شفافية
            except:
                pass  # إذا فشل، استمر بدون شفافية

        except Exception as e:
            logger.error(f"Error adding background image: {e}")

    def _apply_slide_transitions(self, prs):
        """إضافة انتقالات بين الشرائح (محدود في python-pptx)"""
        try:
            # للأسف، python-pptx لا يدعم انتقالات متقدمة
            # لكن يمكن إضافة بعض التحسينات الأساسية
            for slide in prs.slides:
                # يمكن إضافة ملاحظات للشريحة
                notes_slide = slide.notes_slide
                notes_slide.notes_text_frame.text = "Generated by AI Presentation Generator"
        except Exception as e:
            logger.error(f"Error applying transitions: {e}")

# إنشاء مولد العروض التقديمية
generator = PresentationGenerator()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/generate', methods=['POST'])
def generate_presentation():
    try:
        data = request.get_json()
        topic = data.get('topic', '').strip()
        slides_count = data.get('slides_count', app.config['DEFAULT_SLIDES_COUNT'])
        template = data.get('template', 'business')

        # التحقق من صحة البيانات
        if not topic:
            return jsonify({'error': 'الرجاء إدخال موضوع العرض'}), 400

        if len(topic) < 3:
            return jsonify({'error': 'موضوع العرض قصير جداً'}), 400

        if len(topic) > 500:
            return jsonify({'error': 'موضوع العرض طويل جداً'}), 400

        # التحقق من عدد الشرائح
        slides_count = max(app.config['MIN_SLIDES_COUNT'],
                          min(slides_count, app.config['MAX_SLIDES_COUNT']))

        logger.info(f"Starting presentation generation for topic: {topic}")

        # كشف اللغة
        detected_lang = generator.detect_language(topic)
        logger.info(f"Detected language: {detected_lang}")

        # ترجمة إلى الإنجليزية إذا لزم الأمر
        topic_en = generator.translate_to_english(topic, detected_lang)
        if topic_en != topic:
            logger.info(f"Translated topic: {topic_en}")

        # توليد محتوى العرض
        content_data = generator.generate_presentation_content(
            topic_en, detected_lang, slides_count
        )

        # إنشاء العرض التقديمي
        prs = generator.create_presentation(
            topic, content_data, detected_lang, template
        )

        # تطبيق تحسينات إضافية
        generator._apply_slide_transitions(prs)

        # حفظ الملف
        file_stream = io.BytesIO()
        prs.save(file_stream)
        file_stream.seek(0)

        # إنشاء اسم ملف فريد
        safe_topic = "".join(c for c in topic[:20] if c.isalnum() or c in (' ', '-', '_')).strip()
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{safe_topic}_{timestamp}.pptx"

        logger.info(f"Presentation generated successfully: {filename}")

        return send_file(
            file_stream,
            as_attachment=True,
            download_name=filename,
            mimetype="application/vnd.openxmlformats-officedocument.presentationml.presentation"
        )

    except Exception as e:
        logger.error(f"Error generating presentation: {e}")
        error_message = 'حدث خطأ في إنشاء العرض التقديمي'

        # رسائل خطأ محددة
        if 'api key' in str(e).lower():
            error_message = 'خطأ في مفتاح API - تأكد من صحة مفتاح OpenAI'
        elif 'timeout' in str(e).lower():
            error_message = 'انتهت مهلة الاتصال - حاول مرة أخرى'
        elif 'quota' in str(e).lower():
            error_message = 'تم تجاوز حد الاستخدام - حاول لاحقاً'

        return jsonify({'error': error_message}), 500

@app.route('/api/languages')
def get_supported_languages():
    """إرجاع قائمة اللغات المدعومة"""
    return jsonify({
        'languages': app.config['SUPPORTED_LANGUAGES'],
        'count': len(app.config['SUPPORTED_LANGUAGES'])
    })

@app.route('/api/templates')
def get_templates():
    """إرجاع قائمة القوالب المتاحة"""
    return jsonify({
        'templates': app.config['PRESENTATION_TEMPLATES']
    })

@app.route('/api/config')
def get_config():
    """إرجاع إعدادات التطبيق العامة"""
    return jsonify({
        'max_slides': app.config['MAX_SLIDES_COUNT'],
        'min_slides': app.config['MIN_SLIDES_COUNT'],
        'default_slides': app.config['DEFAULT_SLIDES_COUNT'],
        'supported_languages_count': len(app.config['SUPPORTED_LANGUAGES']),
        'templates_count': len(app.config['PRESENTATION_TEMPLATES'])
    })

@app.route('/health')
def health_check():
    """فحص صحة التطبيق"""
    try:
        # فحص اتصال OpenAI
        openai_status = 'ok' if app.config['OPENAI_API_KEY'] and app.config['OPENAI_API_KEY'] != 'YOUR_OPENAI_API_KEY' else 'missing_key'

        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'openai_status': openai_status,
            'version': '1.0.0',
            'features': {
                'multi_language': True,
                'ai_content': True,
                'ai_images': True,
                'custom_templates': True
            }
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'الصفحة غير موجودة'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'خطأ داخلي في الخادم'}), 500

if __name__ == '__main__':
    print("🚀 بدء تشغيل مولد العروض التقديمية بالذكاء الاصطناعي")
    print("🌐 التطبيق يعمل على: http://localhost:5000")
    print("🔑 مفتاح OpenAI API:", "✅ موجود" if app.config['OPENAI_API_KEY'] and app.config['OPENAI_API_KEY'] != 'YOUR_OPENAI_API_KEY' else "❌ غير موجود")
    print("=" * 60)
    app.run(debug=True, host='0.0.0.0', port=5000)
