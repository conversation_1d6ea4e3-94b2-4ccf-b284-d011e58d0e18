#!/usr/bin/env python3
"""
نسخة محسنة ومصححة من مولد العروض التقديمية
تضمن إنشاء محتوى كامل وخلفيات جميلة
"""

from flask import Flask, request, send_file, render_template_string
from pptx import Presentation
from pptx.util import Pt, Inches
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
import io
import uuid
from datetime import datetime
import random

app = Flask(__name__)

# HTML محسن للواجهة
FIXED_HTML = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد العروض التقديمية المحسن</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }
        .fixed-badge {
            background: #27ae60;
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 1em;
            display: inline-block;
            margin-bottom: 15px;
            font-weight: 600;
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 600;
            font-size: 1.1em;
        }
        .form-group textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e6ed;
            border-radius: 10px;
            font-size: 16px;
            font-family: inherit;
            resize: vertical;
            min-height: 120px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        .form-group textarea:focus {
            outline: none;
            border-color: #27ae60;
            background: white;
            box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
        }
        .generate-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 25px;
            padding-top: 25px;
            border-top: 1px solid #e0e6ed;
        }
        .feature {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            transition: transform 0.3s ease;
        }
        .feature:hover {
            transform: translateY(-3px);
        }
        .feature i {
            font-size: 2em;
            color: #27ae60;
            margin-bottom: 10px;
        }
        .feature h3 {
            color: #2c3e50;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        .feature p {
            color: #7f8c8d;
            font-size: 0.8em;
        }
        .examples {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 4px solid #27ae60;
        }
        .examples h4 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        .examples ul {
            list-style: none;
            padding: 0;
        }
        .examples li {
            padding: 8px 0;
            color: #2c3e50;
            border-bottom: 1px solid #d5e8d5;
        }
        .examples li:last-child {
            border-bottom: none;
        }
        .examples li:before {
            content: "💡 ";
            margin-left: 10px;
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="fixed-badge">✅ نسخة محسنة ومصححة</div>
            <h1><i class="fas fa-magic"></i> مولد العروض التقديمية</h1>
            <p>إنشاء عروض تقديمية احترافية مع محتوى كامل وخلفيات جميلة</p>
        </div>

        <form method="post">
            <div class="form-group">
                <label for="topic">
                    <i class="fas fa-lightbulb"></i> اكتب موضوع العرض التقديمي:
                </label>
                <textarea 
                    id="topic" 
                    name="topic" 
                    placeholder="مثال: كيف أتعامل مع الأطفال المشاغبين داخل القسم"
                    required
                ></textarea>
            </div>

            <button type="submit" class="generate-btn">
                <i class="fas fa-wand-magic-sparkles"></i> إنشاء العرض التقديمي المحسن
            </button>
        </form>

        <div class="features">
            <div class="feature">
                <i class="fas fa-file-powerpoint"></i>
                <h3>محتوى كامل</h3>
                <p>شرائح مليئة بالمعلومات المفيدة</p>
            </div>
            <div class="feature">
                <i class="fas fa-palette"></i>
                <h3>خلفيات جميلة</h3>
                <p>تصاميم احترافية وألوان متناسقة</p>
            </div>
            <div class="feature">
                <i class="fas fa-rocket"></i>
                <h3>سريع ومضمون</h3>
                <p>يعمل دائماً بدون مشاكل</p>
            </div>
            <div class="feature">
                <i class="fas fa-download"></i>
                <h3>جاهز للاستخدام</h3>
                <p>ملف PowerPoint قابل للتحميل فوراً</p>
            </div>
        </div>

        <div class="examples">
            <h4>💡 أمثلة للمواضيع التي يمكنك تجربتها:</h4>
            <ul>
                <li>كيف أتعامل مع الأطفال المشاغبين داخل القسم</li>
                <li>استراتيجيات التعلم الفعال للطلاب</li>
                <li>إدارة الوقت في بيئة العمل</li>
                <li>التسويق الرقمي للمبتدئين</li>
                <li>الذكاء الاصطناعي في التعليم</li>
                <li>مهارات التواصل الفعال</li>
            </ul>
        </div>
    </div>
</body>
</html>
'''

class EnhancedPresentationGenerator:
    def __init__(self):
        # قوالب محتوى شاملة ومفصلة
        self.content_database = {
            'تعليم_وإدارة_الصف': {
                'keywords': ['أطفال', 'مشاغب', 'قسم', 'صف', 'طلاب', 'تلاميذ', 'مدرسة', 'تعليم', 'إدارة'],
                'slides': [
                    {
                        'title': 'فهم سلوك الأطفال المشاغبين',
                        'content': [
                            'الأسباب النفسية وراء السلوك المشاغب',
                            'العوامل البيئية والاجتماعية المؤثرة',
                            'الفرق بين النشاط الطبيعي والسلوك المشكل',
                            'أهمية فهم احتياجات كل طفل على حدة'
                        ]
                    },
                    {
                        'title': 'استراتيجيات الوقاية والتدخل المبكر',
                        'content': [
                            'وضع قواعد واضحة ومفهومة للجميع',
                            'إنشاء بيئة تعليمية محفزة وآمنة',
                            'استخدام التعزيز الإيجابي والمكافآت',
                            'تطبيق الأنشطة التفاعلية لتفريغ الطاقة'
                        ]
                    },
                    {
                        'title': 'تقنيات إدارة السلوك الفعالة',
                        'content': [
                            'تقنية إعادة التوجيه والانتباه',
                            'استخدام الإشارات البصرية والصوتية',
                            'تطبيق مبدأ العواقب الطبيعية والمنطقية',
                            'إشراك الطفل في حل المشكلة'
                        ]
                    },
                    {
                        'title': 'بناء علاقات إيجابية مع الأطفال',
                        'content': [
                            'الاستماع الفعال وإظهار الاهتمام الحقيقي',
                            'تقدير نقاط القوة والمواهب الفردية',
                            'إشراك الأطفال في اتخاذ القرارات المناسبة',
                            'تقديم الدعم العاطفي والتشجيع المستمر'
                        ]
                    },
                    {
                        'title': 'التعاون مع الأهل والفريق التعليمي',
                        'content': [
                            'التواصل المنتظم مع أولياء الأمور',
                            'تبادل الاستراتيجيات الناجحة مع الزملاء',
                            'طلب المساعدة من المختصين عند الحاجة',
                            'توثيق التقدم ومتابعة النتائج'
                        ]
                    }
                ]
            },
            'تطوير_مهني': {
                'keywords': ['مهارات', 'تطوير', 'عمل', 'وظيفة', 'مهني', 'إدارة', 'قيادة'],
                'slides': [
                    {
                        'title': 'تحديد أهداف التطوير المهني',
                        'content': [
                            'تقييم المهارات الحالية ونقاط القوة',
                            'تحديد المجالات التي تحتاج للتطوير',
                            'وضع أهداف ذكية وقابلة للقياس',
                            'إنشاء خطة زمنية واقعية للتطوير'
                        ]
                    },
                    {
                        'title': 'استراتيجيات التعلم المستمر',
                        'content': [
                            'الاستفادة من الدورات التدريبية والورش',
                            'القراءة المتخصصة ومتابعة أحدث التطورات',
                            'التعلم من الخبرات والتجارب العملية',
                            'بناء شبكة علاقات مهنية قوية'
                        ]
                    },
                    {
                        'title': 'تطبيق المهارات الجديدة',
                        'content': [
                            'البدء بمشاريع صغيرة لتطبيق ما تعلمته',
                            'طلب التغذية الراجعة من الزملاء والمشرفين',
                            'توثيق النجاحات والدروس المستفادة',
                            'التكيف والتحسين المستمر للأداء'
                        ]
                    },
                    {
                        'title': 'قياس التقدم والنجاح',
                        'content': [
                            'وضع مؤشرات أداء واضحة وقابلة للقياس',
                            'المراجعة الدورية للأهداف والخطط',
                            'الاحتفال بالإنجازات وتقدير التقدم',
                            'تعديل الاستراتيجيات حسب النتائج'
                        ]
                    }
                ]
            },
            'تقنية_وابتكار': {
                'keywords': ['تقنية', 'تكنولوجيا', 'ذكي', 'رقمي', 'ابتكار', 'برمجة'],
                'slides': [
                    {
                        'title': 'مقدمة في التقنيات الحديثة',
                        'content': [
                            'نظرة عامة على التطورات التقنية الحالية',
                            'تأثير التقنية على حياتنا اليومية',
                            'الفرص والتحديات في العصر الرقمي',
                            'أهمية مواكبة التطورات التقنية'
                        ]
                    },
                    {
                        'title': 'التطبيقات العملية والاستخدامات',
                        'content': [
                            'استخدام التقنية في التعليم والتدريب',
                            'التطبيقات في مجال الأعمال والتجارة',
                            'الحلول التقنية للمشاكل اليومية',
                            'الابتكار والإبداع في استخدام التقنية'
                        ]
                    },
                    {
                        'title': 'التحديات والاعتبارات المهمة',
                        'content': [
                            'قضايا الأمان والخصوصية الرقمية',
                            'التوازن بين التقنية والحياة الطبيعية',
                            'التأثير على سوق العمل والمهن',
                            'الاستخدام الأخلاقي والمسؤول للتقنية'
                        ]
                    },
                    {
                        'title': 'المستقبل والتوقعات',
                        'content': [
                            'التوجهات المستقبلية في التقنية',
                            'الاستعداد للتغييرات القادمة',
                            'الفرص الجديدة والمهن الناشئة',
                            'دور الفرد في تشكيل المستقبل التقني'
                        ]
                    }
                ]
            }
        }
        
        # ألوان وتصاميم متنوعة
        self.color_schemes = [
            {
                'name': 'أزرق احترافي',
                'primary': (44, 62, 80),      # أزرق داكن
                'secondary': (52, 152, 219),  # أزرق فاتح
                'accent': (241, 196, 15),     # ذهبي
                'text': (44, 62, 80)          # أزرق داكن
            },
            {
                'name': 'أخضر طبيعي',
                'primary': (39, 174, 96),     # أخضر
                'secondary': (46, 204, 113),  # أخضر فاتح
                'accent': (230, 126, 34),     # برتقالي
                'text': (44, 62, 80)          # أزرق داكن
            },
            {
                'name': 'بنفسجي إبداعي',
                'primary': (142, 68, 173),    # بنفسجي
                'secondary': (155, 89, 182),  # بنفسجي فاتح
                'accent': (241, 196, 15),     # ذهبي
                'text': (44, 62, 80)          # أزرق داكن
            }
        ]
    
    def detect_topic_category(self, topic):
        """كشف فئة الموضوع بدقة أكبر"""
        topic_lower = topic.lower()
        
        for category, data in self.content_database.items():
            for keyword in data['keywords']:
                if keyword in topic_lower:
                    return category
        
        return 'تطوير_مهني'  # افتراضي
    
    def generate_content(self, topic):
        """توليد محتوى مفصل وشامل"""
        category = self.detect_topic_category(topic)
        template = self.content_database[category]
        
        # تخصيص المحتوى حسب الموضوع
        slides = []
        for slide_template in template['slides']:
            slide = {
                'title': slide_template['title'],
                'content': slide_template['content'].copy()
            }
            slides.append(slide)
        
        return {
            'presentation_title': topic,
            'category': category,
            'slides': slides
        }
    
    def add_background_shape(self, slide, color_scheme):
        """إضافة خلفية ملونة جميلة"""
        try:
            # إضافة مستطيل خلفية
            left = Inches(0)
            top = Inches(0)
            width = Inches(10)
            height = Inches(7.5)
            
            shape = slide.shapes.add_shape(
                1,  # مستطيل
                left, top, width, height
            )
            
            # تعيين لون الخلفية
            fill = shape.fill
            fill.solid()
            fill.fore_color.rgb = RGBColor(*color_scheme['secondary'])
            
            # إزالة الحدود
            line = shape.line
            line.fill.background()
            
            # نقل الشكل للخلف
            slide.shapes._spTree.insert(2, slide.shapes._spTree.pop())
            
        except Exception as e:
            print(f"خطأ في إضافة الخلفية: {e}")
    
    def create_presentation(self, topic, content_data):
        """إنشاء عرض تقديمي محسن مع خلفيات"""
        prs = Presentation()
        
        # اختيار نظام ألوان عشوائي
        color_scheme = random.choice(self.color_schemes)
        
        # شريحة العنوان
        title_slide_layout = prs.slide_layouts[0]
        title_slide = prs.slides.add_slide(title_slide_layout)
        
        # إضافة خلفية لشريحة العنوان
        self.add_background_shape(title_slide, color_scheme)
        
        # تعيين العنوان
        title_slide.shapes.title.text = content_data['presentation_title']
        
        # تنسيق العنوان
        title_shape = title_slide.shapes.title
        title_shape.text_frame.paragraphs[0].font.size = Pt(36)
        title_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(*color_scheme['primary'])
        title_shape.text_frame.paragraphs[0].font.bold = True
        
        # العنوان الفرعي
        if len(title_slide.placeholders) > 1:
            subtitle_text = f"عرض تقديمي شامل ومفصل\n{datetime.now().strftime('%Y-%m-%d')}"
            title_slide.placeholders[1].text = subtitle_text
            subtitle_shape = title_slide.placeholders[1]
            subtitle_shape.text_frame.paragraphs[0].font.size = Pt(18)
            subtitle_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(*color_scheme['text'])
        
        # شرائح المحتوى
        for i, slide_data in enumerate(content_data['slides']):
            slide_layout = prs.slide_layouts[1]
            slide = prs.slides.add_slide(slide_layout)
            
            # إضافة خلفية
            self.add_background_shape(slide, color_scheme)
            
            # العنوان
            slide.shapes.title.text = slide_data['title']
            title_shape = slide.shapes.title
            title_shape.text_frame.paragraphs[0].font.size = Pt(30)
            title_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(*color_scheme['primary'])
            title_shape.text_frame.paragraphs[0].font.bold = True
            
            # المحتوى
            if len(slide.placeholders) > 1:
                content_placeholder = slide.placeholders[1]
                text_frame = content_placeholder.text_frame
                text_frame.clear()
                
                for j, point in enumerate(slide_data['content']):
                    p = text_frame.add_paragraph()
                    p.text = f"• {point}"
                    p.font.size = Pt(20)
                    p.font.color.rgb = RGBColor(*color_scheme['text'])
                    p.space_after = Pt(12)
                    
                    # تمييز النقطة الأولى
                    if j == 0:
                        p.font.bold = True
        
        return prs

# إنشاء مولد العروض
generator = EnhancedPresentationGenerator()

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        topic = request.form.get('topic', '').strip()
        
        if not topic:
            return "الرجاء إدخال موضوع العرض", 400
        
        if len(topic) < 5:
            return "الموضوع قصير جداً، الرجاء إدخال موضوع أكثر تفصيلاً", 400
        
        try:
            # توليد المحتوى
            content_data = generator.generate_content(topic)
            
            # إنشاء العرض
            prs = generator.create_presentation(topic, content_data)
            
            # حفظ الملف
            file_stream = io.BytesIO()
            prs.save(file_stream)
            file_stream.seek(0)
            
            # اسم الملف
            safe_topic = "".join(c for c in topic[:30] if c.isalnum() or c in (' ', '-', '_')).strip()
            filename = f"عرض_{safe_topic}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pptx"
            
            return send_file(
                file_stream,
                as_attachment=True,
                download_name=filename,
                mimetype="application/vnd.openxmlformats-officedocument.presentationml.presentation"
            )
            
        except Exception as e:
            return f"حدث خطأ في إنشاء العرض: {str(e)}", 500
    
    return render_template_string(FIXED_HTML)

@app.route('/health')
def health():
    return {
        'status': 'healthy',
        'mode': 'enhanced_fixed',
        'timestamp': datetime.now().isoformat(),
        'features': {
            'full_content': True,
            'beautiful_backgrounds': True,
            'multiple_color_schemes': True,
            'detailed_slides': True
        }
    }

if __name__ == '__main__':
    print("🚀 تشغيل النسخة المحسنة والمصححة")
    print("🌐 التطبيق يعمل على: http://localhost:5004")
    print("✅ محتوى كامل ومفصل")
    print("🎨 خلفيات جميلة وألوان متناسقة")
    print("📊 شرائح احترافية ومليئة بالمعلومات")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5004)
