#!/usr/bin/env python3
"""
تشغيل النسخة المحسنة والمصححة
"""

import os
import sys

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app_fixed import app
    
    print("🚀 تشغيل النسخة المحسنة والمصححة")
    print("🌐 التطبيق يعمل على: http://localhost:5004")
    print("✅ محتوى كامل ومفصل")
    print("🎨 خلفيات جميلة وألوان متناسقة")
    print("📊 شرائح احترافية ومليئة بالمعلومات")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5004)
    
except ImportError as e:
    print(f"❌ خطأ في استيراد التطبيق: {e}")
    print("💡 تأكد من تثبيت جميع المكتبات المطلوبة:")
    print("   pip install flask python-pptx")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل التطبيق: {e}")
    
input("\nاضغط Enter للخروج...")
