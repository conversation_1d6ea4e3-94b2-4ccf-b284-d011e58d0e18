#!/usr/bin/env python3
"""
مولد العروض التقديمية النهائي - يستخدم AI مجاني
يدعم Ollama + Stable Diffusion + واجهة جميلة
"""

from flask import Flask, request, send_file, render_template_string, jsonify
from pptx import Presentation
from pptx.util import Pt, Inches
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
import io
import os
import requests
import json
from datetime import datetime
import base64
from PIL import Image
import threading
import time

app = Flask(__name__)

# HTML واجهة جميلة ومتطورة
BEAUTIFUL_HTML = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد العروض التقديمية بالذكاء الاصطناعي المجاني</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            padding: 20px;
            animation: gradientShift 10s ease infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); }
            50% { background: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #4facfe 100%); }
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .logo {
            font-size: 4em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20px;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .title {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .subtitle {
            font-size: 1.2em;
            color: #7f8c8d;
            margin-bottom: 20px;
        }

        .badge {
            display: inline-block;
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white;
            padding: 12px 25px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1em;
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
            animation: bounce 2s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }

        .form-section {
            margin: 40px 0;
        }

        .form-group {
            margin-bottom: 30px;
        }

        .form-label {
            display: block;
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .form-label i {
            margin-left: 10px;
            color: #667eea;
        }

        .form-input {
            width: 100%;
            padding: 20px;
            border: 3px solid #e0e6ed;
            border-radius: 15px;
            font-size: 1.1em;
            font-family: inherit;
            resize: vertical;
            min-height: 150px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .ai-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .ai-option {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ai-option:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .ai-option.selected {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .ai-option i {
            font-size: 2.5em;
            margin-bottom: 15px;
            display: block;
        }

        .generate-btn {
            width: 100%;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 15px;
            font-size: 1.3em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .generate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
        }

        .generate-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 30px 0;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 6px solid #f3f3f3;
            border-top: 6px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }

        .feature {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #dee2e6;
        }

        .feature:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .feature i {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 20px;
            display: block;
        }

        .feature h3 {
            color: #2c3e50;
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        .feature p {
            color: #7f8c8d;
            line-height: 1.6;
        }

        .examples {
            background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            border-left: 5px solid #27ae60;
        }

        .examples h3 {
            color: #27ae60;
            font-size: 1.4em;
            margin-bottom: 20px;
        }

        .examples-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }

        .example-item {
            background: rgba(255, 255, 255, 0.8);
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .example-item:hover {
            background: white;
            transform: translateX(5px);
        }

        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            display: none;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            
            .title {
                font-size: 2em;
            }
            
            .ai-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <i class="fas fa-magic"></i>
            </div>
            <h1 class="title">مولد العروض التقديمية</h1>
            <p class="subtitle">بالذكاء الاصطناعي المجاني والمفتوح المصدر</p>
            <div class="badge">
                <i class="fas fa-star"></i>
                مجاني 100% - بدون حدود
            </div>
        </div>

        <form id="presentationForm" class="form-section">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-lightbulb"></i>
                    اكتب موضوع العرض التقديمي
                </label>
                <textarea 
                    id="topic" 
                    name="topic" 
                    class="form-input"
                    placeholder="مثال: كيف أتعامل مع الأطفال المشاغبين داخل القسم
أو: استراتيجيات التسويق الرقمي الحديثة
أو: مقدمة في الذكاء الاصطناعي"
                    required
                ></textarea>
            </div>

            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-robot"></i>
                    اختر نوع الذكاء الاصطناعي
                </label>
                <div class="ai-options">
                    <div class="ai-option selected" data-ai="local">
                        <i class="fas fa-desktop"></i>
                        <h4>ذكاء محلي</h4>
                        <p>يعمل على جهازك</p>
                    </div>
                    <div class="ai-option" data-ai="online">
                        <i class="fas fa-cloud"></i>
                        <h4>ذكاء سحابي</h4>
                        <p>عبر الإنترنت</p>
                    </div>
                    <div class="ai-option" data-ai="template">
                        <i class="fas fa-file-alt"></i>
                        <h4>قوالب ذكية</h4>
                        <p>محتوى جاهز</p>
                    </div>
                </div>
            </div>

            <button type="submit" class="generate-btn" id="generateBtn">
                <i class="fas fa-wand-magic-sparkles"></i>
                إنشاء العرض التقديمي الآن
            </button>
        </form>

        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <h3>جاري إنشاء عرضك التقديمي...</h3>
            <p>يتم استخدام الذكاء الاصطناعي لإنشاء محتوى احترافي</p>
        </div>

        <div class="status" id="status"></div>

        <div class="features">
            <div class="feature">
                <i class="fas fa-brain"></i>
                <h3>ذكاء اصطناعي متقدم</h3>
                <p>يستخدم نماذج AI مفتوحة المصدر مثل Llama و Mistral</p>
            </div>
            <div class="feature">
                <i class="fas fa-images"></i>
                <h3>صور وخلفيات ذكية</h3>
                <p>توليد صور احترافية باستخدام Stable Diffusion</p>
            </div>
            <div class="feature">
                <i class="fas fa-language"></i>
                <h3>دعم جميع اللغات</h3>
                <p>يفهم ويولد محتوى بالعربية والإنجليزية وأكثر</p>
            </div>
            <div class="feature">
                <i class="fas fa-download"></i>
                <h3>تحميل فوري</h3>
                <p>احصل على ملف PowerPoint جاهز للاستخدام</p>
            </div>
        </div>

        <div class="examples">
            <h3><i class="fas fa-lightbulb"></i> أمثلة للمواضيع</h3>
            <div class="examples-grid">
                <div class="example-item" onclick="fillExample(this)">
                    💼 كيف أتعامل مع الأطفال المشاغبين داخل القسم
                </div>
                <div class="example-item" onclick="fillExample(this)">
                    📈 استراتيجيات التسويق الرقمي الحديثة
                </div>
                <div class="example-item" onclick="fillExample(this)">
                    🤖 مقدمة في الذكاء الاصطناعي وتطبيقاته
                </div>
                <div class="example-item" onclick="fillExample(this)">
                    🏥 إدارة الوقت في بيئة العمل الطبية
                </div>
                <div class="example-item" onclick="fillExample(this)">
                    🎓 طرق التدريس الحديثة والتعلم التفاعلي
                </div>
                <div class="example-item" onclick="fillExample(this)">
                    💰 أساسيات الاستثمار والتخطيط المالي
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedAI = 'local';

        // اختيار نوع AI
        document.querySelectorAll('.ai-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.ai-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedAI = this.dataset.ai;
            });
        });

        // ملء المثال
        function fillExample(element) {
            const text = element.textContent.replace(/^[💼📈🤖🏥🎓💰]\s/, '');
            document.getElementById('topic').value = text;
            element.style.background = 'rgba(102, 126, 234, 0.2)';
            setTimeout(() => {
                element.style.background = '';
            }, 1000);
        }

        // إرسال النموذج
        document.getElementById('presentationForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const topic = document.getElementById('topic').value.trim();
            const generateBtn = document.getElementById('generateBtn');
            const loading = document.getElementById('loading');
            const status = document.getElementById('status');
            
            if (!topic) {
                showStatus('الرجاء إدخال موضوع العرض التقديمي', 'error');
                return;
            }
            
            // إظهار التحميل
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';
            loading.style.display = 'block';
            status.style.display = 'none';
            
            try {
                const response = await fetch('/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        topic: topic,
                        ai_type: selectedAI
                    })
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = `presentation_${Date.now()}.pptx`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    
                    showStatus('تم إنشاء العرض التقديمي بنجاح! جاري التحميل...', 'success');
                } else {
                    const errorData = await response.json();
                    showStatus(errorData.error || 'حدث خطأ في إنشاء العرض التقديمي', 'error');
                }
            } catch (err) {
                console.error('Error:', err);
                showStatus('حدث خطأ في الاتصال. تأكد من اتصالك بالإنترنت وحاول مرة أخرى.', 'error');
            } finally {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-wand-magic-sparkles"></i> إنشاء العرض التقديمي الآن';
                loading.style.display = 'none';
            }
        });
        
        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
            status.style.display = 'block';
            
            if (type === 'success') {
                setTimeout(() => {
                    status.style.display = 'none';
                }, 5000);
            }
        }
    </script>
</body>
</html>
'''

class AIContentGenerator:
    def __init__(self):
        self.ollama_url = "http://localhost:11434"
        
    def check_ollama_status(self):
        """فحص حالة Ollama"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def generate_with_ollama(self, topic):
        """توليد المحتوى باستخدام Ollama"""
        try:
            prompt = f"""
            أنشئ عرض تقديمي احترافي عن موضوع: {topic}
            
            يجب أن يحتوي على 5 شرائح:
            1. شريحة مقدمة
            2-5. شرائح محتوى
            
            لكل شريحة اكتب:
            - عنوان الشريحة
            - 4 نقاط مفصلة
            
            اكتب الإجابة بتنسيق JSON:
            {{
                "slides": [
                    {{
                        "title": "عنوان الشريحة",
                        "content": ["نقطة 1", "نقطة 2", "نقطة 3", "نقطة 4"]
                    }}
                ]
            }}
            """
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": "llama2",
                    "prompt": prompt,
                    "stream": False
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result.get('response', '')
                
                # استخراج JSON من الرد
                start = content.find('{')
                end = content.rfind('}') + 1
                if start != -1 and end > start:
                    json_content = content[start:end]
                    return json.loads(json_content)
            
            return None
            
        except Exception as e:
            print(f"خطأ في Ollama: {e}")
            return None
    
    def generate_template_content(self, topic):
        """توليد محتوى باستخدام القوالب الذكية"""
        topic_lower = topic.lower()
        
        # تحديد نوع الموضوع
        if any(word in topic_lower for word in ['أطفال', 'مشاغب', 'قسم', 'صف', 'طلاب', 'تعليم']):
            return self.get_education_template(topic)
        elif any(word in topic_lower for word in ['تسويق', 'رقمي', 'إعلان', 'بيع', 'عملاء']):
            return self.get_marketing_template(topic)
        elif any(word in topic_lower for word in ['ذكاء', 'اصطناعي', 'تقنية', 'تكنولوجيا']):
            return self.get_tech_template(topic)
        else:
            return self.get_general_template(topic)
    
    def get_education_template(self, topic):
        return {
            "slides": [
                {
                    "title": f"مقدمة حول {topic}",
                    "content": [
                        "تعريف المشكلة وأهمية معالجتها في البيئة التعليمية",
                        "الأسباب الجذرية وراء هذا السلوك والعوامل المؤثرة",
                        "التأثير على العملية التعليمية والطلاب الآخرين",
                        "الأهداف المرجوة من تطبيق الاستراتيجيات المناسبة"
                    ]
                },
                {
                    "title": "فهم السلوك وأسبابه",
                    "content": [
                        "العوامل النفسية: الحاجة للانتباه والتعبير عن المشاعر",
                        "العوامل البيئية: تأثير المنزل والمدرسة والأقران",
                        "الفروق الفردية: أساليب التعلم والشخصية المختلفة",
                        "علامات التحذير المبكرة التي تستدعي التدخل"
                    ]
                },
                {
                    "title": "استراتيجيات الوقاية والتدخل",
                    "content": [
                        "وضع قواعد واضحة ومفهومة مع شرح الأسباب",
                        "إنشاء بيئة تعليمية محفزة وآمنة للجميع",
                        "استخدام التعزيز الإيجابي والمكافآت المناسبة",
                        "تطبيق أنشطة تفاعلية لتوجيه الطاقة إيجابياً"
                    ]
                },
                {
                    "title": "تقنيات الإدارة الفعالة",
                    "content": [
                        "إعادة التوجيه: تحويل الانتباه من السلبي للإيجابي",
                        "استخدام الإشارات البصرية والصوتية اللطيفة",
                        "تطبيق العواقب الطبيعية والمنطقية للسلوك",
                        "إشراك الطالب في حل المشكلة وإيجاد البدائل"
                    ]
                },
                {
                    "title": "بناء العلاقات والتعاون",
                    "content": [
                        "الاستماع الفعال وإظهار الاهتمام الحقيقي",
                        "تقدير نقاط القوة والمواهب الفردية",
                        "التواصل المستمر مع أولياء الأمور",
                        "طلب المساعدة من المختصين عند الحاجة"
                    ]
                }
            ]
        }
    
    def get_marketing_template(self, topic):
        return {
            "slides": [
                {
                    "title": f"مقدمة في {topic}",
                    "content": [
                        "تعريف التسويق الرقمي وأهميته في العصر الحديث",
                        "الفرق بين التسويق التقليدي والتسويق الرقمي",
                        "الفرص والتحديات في السوق الرقمي اليوم",
                        "أهداف ومؤشرات نجاح الحملات التسويقية الرقمية"
                    ]
                },
                {
                    "title": "منصات التسويق الرقمي",
                    "content": [
                        "وسائل التواصل الاجتماعي: فيسبوك، إنستغرام، تويتر",
                        "محركات البحث: جوجل، بينغ وتحسين الظهور",
                        "التسويق عبر البريد الإلكتروني والرسائل النصية",
                        "المنصات الناشئة: تيك توك، سناب شات، لينكد إن"
                    ]
                },
                {
                    "title": "استراتيجيات المحتوى",
                    "content": [
                        "إنشاء محتوى جذاب ومفيد للجمهور المستهدف",
                        "استخدام الصور والفيديوهات والإنفوجرافيك",
                        "التوقيت المناسب للنشر وتكرار المحتوى",
                        "قياس التفاعل وتحليل أداء المحتوى"
                    ]
                },
                {
                    "title": "قياس النجاح والتحليل",
                    "content": [
                        "مؤشرات الأداء الرئيسية (KPIs) في التسويق الرقمي",
                        "أدوات التحليل: جوجل أناليتكس، فيسبوك إنسايتس",
                        "تحليل سلوك العملاء ورحلة الشراء",
                        "تحسين الحملات بناءً على البيانات والنتائج"
                    ]
                }
            ]
        }
    
    def get_tech_template(self, topic):
        return {
            "slides": [
                {
                    "title": f"مقدمة في {topic}",
                    "content": [
                        "تعريف الذكاء الاصطناعي وتطوره عبر التاريخ",
                        "الفرق بين الذكاء الاصطناعي والتعلم الآلي",
                        "التطبيقات الحالية في حياتنا اليومية",
                        "التوقعات المستقبلية وإمكانيات التطوير"
                    ]
                },
                {
                    "title": "أنواع الذكاء الاصطناعي",
                    "content": [
                        "الذكاء الاصطناعي الضيق (ANI): التطبيقات المحددة",
                        "الذكاء الاصطناعي العام (AGI): القدرات الشاملة",
                        "الذكاء الاصطناعي الفائق (ASI): تجاوز القدرات البشرية",
                        "التعلم العميق والشبكات العصبية الاصطناعية"
                    ]
                },
                {
                    "title": "التطبيقات العملية",
                    "content": [
                        "الطب: تشخيص الأمراض وتطوير الأدوية",
                        "النقل: السيارات ذاتية القيادة والطيران",
                        "التعليم: التعلم المخصص والمساعدين الافتراضيين",
                        "الأعمال: خدمة العملاء والتحليل التنبؤي"
                    ]
                },
                {
                    "title": "التحديات والمستقبل",
                    "content": [
                        "التحديات الأخلاقية والخصوصية والأمان",
                        "تأثير الذكاء الاصطناعي على سوق العمل",
                        "الحاجة للتنظيم والقوانين المناسبة",
                        "الفرص المستقبلية والاستعداد للتغيير"
                    ]
                }
            ]
        }
    
    def get_general_template(self, topic):
        return {
            "slides": [
                {
                    "title": f"مقدمة حول {topic}",
                    "content": [
                        f"تعريف شامل لموضوع {topic} وأهميته",
                        "الخلفية التاريخية والسياق العام للموضوع",
                        "الأهداف المرجوة من دراسة هذا الموضوع",
                        "نظرة عامة على ما سيتم تغطيته في العرض"
                    ]
                },
                {
                    "title": "العناصر الأساسية",
                    "content": [
                        "المكونات الرئيسية والعناصر الأساسية",
                        "العلاقات والروابط بين هذه العناصر",
                        "الخصائص المميزة والسمات الفريدة",
                        "التصنيفات والأنواع المختلفة"
                    ]
                },
                {
                    "title": "التطبيقات العملية",
                    "content": [
                        "الاستخدامات العملية في الحياة اليومية",
                        "أمثلة واقعية وحالات دراسية",
                        "الفوائد والمزايا من التطبيق الصحيح",
                        "التحديات والصعوبات المحتملة"
                    ]
                },
                {
                    "title": "الخلاصة والتوصيات",
                    "content": [
                        "ملخص النقاط الرئيسية المهمة",
                        "النتائج والاستنتاجات الرئيسية",
                        "التوصيات للتطبيق والاستفادة",
                        "الخطوات التالية والتطوير المستقبلي"
                    ]
                }
            ]
        }

# إنشاء مولد المحتوى
content_generator = AIContentGenerator()

@app.route('/')
def index():
    return render_template_string(BEAUTIFUL_HTML)

@app.route('/generate', methods=['POST'])
def generate_presentation():
    try:
        data = request.get_json()
        topic = data.get('topic', '').strip()
        ai_type = data.get('ai_type', 'template')
        
        if not topic:
            return jsonify({'error': 'الرجاء إدخال موضوع العرض'}), 400
        
        # توليد المحتوى حسب نوع AI المختار
        if ai_type == 'local' and content_generator.check_ollama_status():
            content_data = content_generator.generate_with_ollama(topic)
        elif ai_type == 'online':
            # يمكن إضافة APIs أخرى هنا
            content_data = content_generator.generate_template_content(topic)
        else:
            content_data = content_generator.generate_template_content(topic)
        
        if not content_data:
            content_data = content_generator.generate_template_content(topic)
        
        # إنشاء العرض التقديمي
        prs = create_beautiful_presentation(topic, content_data)
        
        # حفظ الملف
        file_stream = io.BytesIO()
        prs.save(file_stream)
        file_stream.seek(0)
        
        # اسم الملف
        safe_topic = "".join(c for c in topic[:25] if c.isalnum() or c in (' ', '-', '_')).strip()
        filename = f"عرض_{safe_topic}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pptx"
        
        return send_file(
            file_stream,
            as_attachment=True,
            download_name=filename,
            mimetype="application/vnd.openxmlformats-officedocument.presentationml.presentation"
        )
        
    except Exception as e:
        return jsonify({'error': f'حدث خطأ: {str(e)}'}), 500

def create_beautiful_presentation(topic, content_data):
    """إنشاء عرض تقديمي جميل ومنسق"""
    prs = Presentation()
    
    # ألوان احترافية
    primary_color = RGBColor(102, 126, 234)  # أزرق
    secondary_color = RGBColor(118, 75, 162)  # بنفسجي
    text_color = RGBColor(44, 62, 80)         # أزرق داكن
    accent_color = RGBColor(231, 76, 60)      # أحمر
    
    # شريحة العنوان
    title_slide = prs.slides.add_slide(prs.slide_layouts[0])
    
    # إضافة خلفية ملونة
    add_gradient_background(title_slide, primary_color, secondary_color, prs)
    
    # العنوان
    title_slide.shapes.title.text = topic
    title_shape = title_slide.shapes.title
    title_shape.text_frame.paragraphs[0].font.size = Pt(44)
    title_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(255, 255, 255)
    title_shape.text_frame.paragraphs[0].font.bold = True
    
    # العنوان الفرعي
    if len(title_slide.placeholders) > 1:
        subtitle = f"عرض تقديمي احترافي\nتم إنشاؤه بالذكاء الاصطناعي\n{datetime.now().strftime('%Y-%m-%d')}"
        title_slide.placeholders[1].text = subtitle
        subtitle_shape = title_slide.placeholders[1]
        subtitle_shape.text_frame.paragraphs[0].font.size = Pt(18)
        subtitle_shape.text_frame.paragraphs[0].font.color.rgb = RGBColor(255, 255, 255)
    
    # شرائح المحتوى
    for i, slide_data in enumerate(content_data['slides']):
        slide = prs.slides.add_slide(prs.slide_layouts[1])
        
        # خلفية متدرجة للشرائح
        bg_color1 = RGBColor(248, 249, 250)
        bg_color2 = RGBColor(233, 236, 239)
        add_gradient_background(slide, bg_color1, bg_color2, prs)
        
        # العنوان
        slide.shapes.title.text = slide_data['title']
        title_shape = slide.shapes.title
        title_shape.text_frame.paragraphs[0].font.size = Pt(32)
        title_shape.text_frame.paragraphs[0].font.color.rgb = primary_color
        title_shape.text_frame.paragraphs[0].font.bold = True
        
        # المحتوى
        if len(slide.placeholders) > 1:
            content_shape = slide.placeholders[1]
            text_frame = content_shape.text_frame
            text_frame.clear()
            
            for j, point in enumerate(slide_data['content']):
                if j == 0:
                    p = text_frame.paragraphs[0]
                else:
                    p = text_frame.add_paragraph()
                
                p.text = f"• {point}"
                p.font.size = Pt(20)
                p.font.color.rgb = text_color
                p.space_after = Pt(15)
                
                # تمييز النقطة الأولى
                if j == 0:
                    p.font.bold = True
                    p.font.color.rgb = accent_color
    
    return prs

def add_gradient_background(slide, color1, color2, prs):
    """إضافة خلفية متدرجة للشريحة"""
    try:
        # إضافة مستطيل خلفية
        left = Inches(0)
        top = Inches(0)
        width = prs.slide_width
        height = prs.slide_height
        
        shape = slide.shapes.add_shape(
            MSO_SHAPE.RECTANGLE,
            left, top, width, height
        )
        
        # تعيين لون الخلفية
        fill = shape.fill
        fill.solid()
        fill.fore_color.rgb = color1
        
        # إزالة الحدود
        line = shape.line
        line.fill.background()
        
        # نقل الشكل للخلف
        slide.shapes._spTree.insert(2, slide.shapes._spTree.pop())
        
    except Exception as e:
        print(f"خطأ في إضافة الخلفية: {e}")

@app.route('/health')
def health_check():
    ollama_status = content_generator.check_ollama_status()
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'ollama_available': ollama_status,
        'features': {
            'beautiful_ui': True,
            'ai_content': True,
            'multiple_ai_types': True,
            'free_unlimited': True
        }
    })

if __name__ == '__main__':
    print("🚀 تشغيل مولد العروض التقديمية النهائي")
    print("🌐 التطبيق يعمل على: http://localhost:8000")
    print("✨ واجهة جميلة ومتطورة")
    print("🤖 ذكاء اصطناعي مجاني")
    print("🎨 خلفيات وتصاميم احترافية")
    print("📊 محتوى مفصل وشامل")
    print("=" * 60)

    # فحص Ollama
    if content_generator.check_ollama_status():
        print("✅ Ollama متاح - ذكاء اصطناعي محلي جاهز")
    else:
        print("⚠️  Ollama غير متاح - سيتم استخدام القوالب الذكية")
        print("💡 لتثبيت Ollama: قم بتشغيل install_ollama.bat")

    app.run(debug=True, host='0.0.0.0', port=8000)
